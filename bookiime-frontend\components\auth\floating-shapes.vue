<template>
  <div class="absolute inset-0 pointer-events-none">
    <div
      class="absolute top-1/4 left-1/6 w-4 h-4 bg-tertiary-400 rounded-full animate-bounce opacity-70"
      style="animation-delay: 0.2s"
    ></div>
    <div
      class="absolute top-1/3 right-1/3 w-3 h-3 bg-green-400 rounded-full animate-bounce opacity-70"
      style="animation-delay: 0.8s"
    ></div>
    <div
      class="absolute bottom-1/3 left-1/3 w-5 h-5 bg-secondary-300 rounded-full animate-bounce opacity-70"
      style="animation-delay: 1.2s"
    ></div>
    <div
      class="absolute bottom-1/3 right-1/4 w-2 h-2 bg-white rounded-full animate-bounce opacity-80"
      style="animation-delay: 0.6s"
    ></div>

    <ClientOnly>
      <div
        class="lg:hidden absolute w-2 h-2 bg-primary-400 rounded-full opacity-30 animate-bounce"
        v-for="i in 12"
        :key="i"
        :style="{
          left: Math.random() * 100 + '%',
          top: Math.random() * 100 + '%',
          animationDelay: Math.random() * 3 + 's',
          animationDuration: 2 + Math.random() * 2 + 's',
        }"
      ></div>
    </ClientOnly>
  </div>
</template>
