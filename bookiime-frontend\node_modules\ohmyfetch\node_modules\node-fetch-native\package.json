{"name": "node-fetch-native", "version": "0.1.8", "description": "A better redistribution of `node-fetch`", "repository": "unjs/node-fetch-native", "license": "MIT", "type": "module", "exports": {".": {"node": {"require": "./lib/index.cjs", "import": "./dist/index.mjs"}, "import": "./dist/native.mjs"}, "./polyfill": {"node": {"require": "./dist/polyfill.cjs", "import": "./dist/polyfill.mjs"}, "import": "./lib/empty.mjs"}, "./src/index.js": {"import": "./dist/index.mjs"}}, "main": "./lib/index.cjs", "module": "./dist/index.mjs", "types": "./lib/index.d.ts", "files": ["dist", "lib"], "devDependencies": {"@nuxtjs/eslint-config-typescript": "^11.0.0", "@vitest/coverage-c8": "^0.24.3", "abort-controller": "^3.0.0", "eslint": "^8.25.0", "node-fetch": "^3.2.10", "standard-version": "^9.5.0", "typescript": "^4.8.4", "unbuild": "^0.9.4", "vitest": "^0.24.3"}, "packageManager": "pnpm@7.13.4", "scripts": {"build": "unbuild", "lint": "eslint --ext .ts,.js,.mjs,.cjs .", "release": "pnpm test && standard-version && git push --follow-tags && pnpm publish", "test": "pnpm lint && pnpm build && vitest run --coverage"}}