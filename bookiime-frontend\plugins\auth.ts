import { useUser } from "~/composables/useUser";

export default defineNuxtPlugin(() => {
    // This is for authentication management through cookies
    const cookie = useCookie('ac2cfd-6297-5ccb-4412-97d3', { // jwt cookie name
        maxAge: 60 * 60 * 24 * 2,
        path: '/',
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict'
    })

    // This is for managing authentication state
    const auth = useState('auth', () => ({
        token: cookie.value || null,
    }))

    // This is for user management
    // It uses the user cookie to store user data
    //But it is not used in this plugin directly when logging in
    // It is used in other parts of the application
    const { user } = useUser();

    const login = (response: {access_token: string}) => {
        const token = response.access_token
        cookie.value = token
        auth.value.token = token
    }

    const logout = async () => {
        auth.value.token = null
        cookie.value = null
        user.value = null
        await navigateTo('/login')
    }

    const isLoggedIn = computed(() => !!auth.value.token && !!Object.keys(user).length)

    return {
        provide: {
            login,
            logout,
            auth,
            isLoggedIn,}
    }
})


