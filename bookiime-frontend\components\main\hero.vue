<template>
  <section
    class="relative min-h-screen flex items-center justify-center overflow-hidden bg-white-200"
  >
    <div class="absolute inset-0 overflow-hidden">
      <div
        class="absolute w-96 h-96 rounded-full opacity-20 blur-3xl animate-pulse"
        :style="`background: radial-gradient(circle, var(--color-primary-400) 0%, transparent 70%); transform: translate(${
          mousePosition.x * 0.02
        }px, ${mousePosition.y * 0.02}px)`"
      />
      <div
        class="absolute w-80 h-80 rounded-full opacity-15 blur-3xl animate-pulse top-1/4 right-1/4"
        :style="`background: radial-gradient(circle, var(--color-tertiary-500) 0%, transparent 70%); transform: translate(${
          mousePosition.x * -0.015
        }px, ${mousePosition.y * -0.015}px); animation-delay: 1s`"
      />
      <div
        class="absolute w-72 h-72 rounded-full opacity-10 blur-3xl animate-pulse bottom-1/4 left-1/3"
        :style="`background: radial-gradient(circle, var(--color-secondary-400) 0%, transparent 70%); transform: translate(${
          mousePosition.x * 0.01
        }px, ${mousePosition.y * 0.01}px); animation-delay: 2s`"
      />

      <div
        v-for="(shape, i) in floatingShapes"
        :key="i"
        class="absolute animate-float opacity-30"
        :class="shape.classes"
        :style="`left: ${shape.x}%; top: ${shape.y}%; animation-delay: ${shape.delay}s; animation-duration: ${shape.duration}s`"
      />
    </div>

    <div class="absolute inset-0 pointer-events-none hidden lg:block">
      <div
        v-for="(badge, index) in featureBadges"
        :key="index"
        class="absolute backdrop-blur-sm rounded-2xl p-4 shadow-xl border border-white/20 animate-float-gentle"
        :class="badge.bgClass"
        :style="`left: ${badge.x}%; top: ${badge.y}%; animation-delay: ${badge.delay}s`"
      >
        <div class="flex items-center space-x-3">
          <div
            class="w-8 h-8 rounded-lg bg-white/20 flex items-center justify-center"
          >
            <Icon :name="badge.icon" class="w-4 h-4 text-white" />
          </div>
          <div>
            <div class="text-white font-semibold text-sm">
              {{ badge.title }}
            </div>
            <div class="text-white/80 text-xs">{{ badge.subtitle }}</div>
          </div>
        </div>
      </div>
    </div>

    <div class="relative z-10 px-6 py-20 max-w-7xl mx-auto text-center">
      <div class="mb-8" :class="{ 'animate-fade-in-up': mounted }">
        <div
          class="inline-flex items-center px-8 py-4 bg-gradient-to-r from-green-500/20 to-green-600/20 rounded-full border border-green-500/30 backdrop-blur-sm mb-8 shadow-lg"
        >
          <div class="w-3 h-3 bg-green-500 rounded-full animate-pulse mr-3" />
          <Icon name="lucide:rocket" class="w-5 h-5 text-green-600 mr-2" />
          <span class="text-green-700 text-lg font-bold"
            >Now Live & Ready!</span
          >
          <div class="ml-3 flex space-x-1">
            <div
              class="w-1 h-1 bg-green-500 rounded-full animate-bounce"
              style="animation-delay: 0s"
            />
            <div
              class="w-1 h-1 bg-green-500 rounded-full animate-bounce"
              style="animation-delay: 0.2s"
            />
            <div
              class="w-1 h-1 bg-green-500 rounded-full animate-bounce"
              style="animation-delay: 0.4s"
            />
          </div>
        </div>
      </div>

      <div
        class="mb-8"
        :class="{ 'animate-fade-in-up': mounted }"
        style="animation-delay: 0.2s"
      >
        <h1
          class="text-6xl md:text-8xl lg:text-9xl font-black text-neutral-1000 mb-6 leading-none tracking-tight"
        >
          <span class="block relative">
            Transform Your
            <div
              class="absolute -top-4 -right-4 w-12 h-12 bg-primary-400/20 rounded-full animate-ping"
            />
          </span>
          <span class="block gt-primary relative">
            Business
            <div
              class="absolute inset-0 bg-gradient-to-r from-primary-400/10 via-tertiary-400/10 to-primary-500/10 blur-2xl animate-pulse -z-10"
            />
          </span>
          <span class="block">With Bookiime</span>
        </h1>
      </div>

      <div
        class="mb-6"
        :class="{ 'animate-fade-in-up': mounted }"
        style="animation-delay: 0.4s"
      >
        <p
          class="text-2xl md:text-4xl text-neutral-800 max-w-5xl mx-auto leading-relaxed font-light"
        >
          Ghana's most powerful booking platform is here.
          <span class="font-semibold gt-secondary block mt-2">
            Start accepting bookings in under 5 minutes.
          </span>
        </p>
      </div>

      <div
        class="mb-12"
        :class="{ 'animate-fade-in-up': mounted }"
        style="animation-delay: 0.6s"
      >
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
          <div
            class="flex items-center justify-center space-x-2 text-neutral-700"
          >
            <Icon name="lucide:smartphone" class="w-5 h-5 text-primary-500" />
            <span class="text-lg font-medium">Mobile Money Ready</span>
          </div>
          <div
            class="flex items-center justify-center space-x-2 text-neutral-700"
          >
            <Icon name="lucide:zap" class="w-5 h-5 text-tertiary-500" />
            <span class="text-lg font-medium">Instant Notifications</span>
          </div>
          <div
            class="flex items-center justify-center space-x-2 text-neutral-700"
          >
            <Icon name="lucide:shield-check" class="w-5 h-5 text-green-600" />
            <span class="text-lg font-medium">100% Secure</span>
          </div>
        </div>
      </div>

      <div
        class="mb-16"
        :class="{ 'animate-fade-in-up': mounted }"
        style="animation-delay: 0.8s"
      >
        <div
          class="flex flex-col md:flex-row items-center justify-center space-y-4 md:space-y-0 md:space-x-6"
        >
          <button
            @click="handleGetStarted"
            class="group relative px-10 py-5 primary-background text-white font-bold text-xl rounded-2xl shadow-2xl shadow-primary-500/30 hover:shadow-primary-500/50 transition-all duration-300 transform hover:scale-105 active:scale-95 overflow-hidden"
          >
            <span class="relative z-10 flex items-center">
              Get Started Free
              <div class="ml-3 transition-transform group-hover:translate-x-1">
                <Icon name="lucide:arrow-right" class="w-6 h-6" />
              </div>
            </span>
            <div
              class="absolute inset-0 bg-gradient-to-r from-primary-600 via-tertiary-600 to-primary-700 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"
            />
            <div
              class="absolute -top-2 -right-2 w-6 h-6 bg-white/30 rounded-full animate-pulse"
            />
          </button>

          <button
            @click="handleWatchDemo"
            class="group px-10 py-5 bg-white/80 backdrop-blur-sm text-neutral-800 font-semibold text-xl rounded-2xl border-2 border-neutral-200 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 active:scale-95"
          >
            <span class="flex items-center">
              <Icon
                name="lucide:play-circle"
                class="w-6 h-6 mr-3 text-primary-500"
              />
              Watch Demo
            </span>
          </button>
        </div>

        <div
          class="mt-8 flex items-center justify-center space-x-8 text-neutral-600"
        >
          <div class="text-center">
            <div class="text-2xl font-bold text-primary-500">1000+</div>
            <div class="text-sm">Happy Businesses</div>
          </div>
          <div class="w-px h-8 bg-neutral-300" />
          <div class="text-center">
            <div class="text-2xl font-bold text-tertiary-500">50K+</div>
            <div class="text-sm">Bookings Made</div>
          </div>
          <div class="w-px h-8 bg-neutral-300" />
          <div class="text-center">
            <div class="text-2xl font-bold text-green-600">24/7</div>
            <div class="text-sm">Support</div>
          </div>
        </div>
      </div>

      <div
        class="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce"
      >
        <div
          class="w-8 h-12 border-2 border-neutral-400 rounded-full flex justify-center"
        >
          <div class="w-1 h-3 bg-neutral-400 rounded-full mt-2 animate-pulse" />
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from "vue";

const emit = defineEmits(["get-started", "watch-demo"]);
const mousePosition = ref({ x: 0, y: 0 });
const mounted = ref(false);

const floatingShapes = ref([
  {
    x: 10,
    y: 20,
    classes: "w-4 h-4 bg-primary-400/30 rounded-full",
    delay: 0,
    duration: 6,
  },
  {
    x: 85,
    y: 15,
    classes: "w-6 h-6 bg-tertiary-500/30 rounded-lg rotate-45",
    delay: 1,
    duration: 8,
  },
  {
    x: 15,
    y: 70,
    classes: "w-5 h-5 bg-secondary-400/30 rounded-full",
    delay: 2,
    duration: 7,
  },
  {
    x: 80,
    y: 75,
    classes: "w-3 h-3 bg-primary-500/30 rounded-full",
    delay: 3,
    duration: 5,
  },
  {
    x: 45,
    y: 10,
    classes: "w-4 h-4 bg-tertiary-400/30 rounded-lg rotate-12",
    delay: 4,
    duration: 9,
  },
  {
    x: 70,
    y: 45,
    classes: "w-2 h-2 bg-primary-400/30 rounded-full",
    delay: 5,
    duration: 6,
  },
]);

const featureBadges = ref([
  {
    x: 8,
    y: 25,
    icon: "lucide:calendar-check",
    title: "Smart Scheduling",
    subtitle: "AI-powered booking",
    bgClass: "bg-primary-500/20",
    delay: 0,
  },
  {
    x: 82,
    y: 30,
    icon: "lucide:message-circle",
    title: "Auto Reminders",
    subtitle: "WhatsApp & SMS",
    bgClass: "bg-green-500/20",
    delay: 1,
  },
  {
    x: 12,
    y: 65,
    icon: "lucide:credit-card",
    title: "Easy Payments",
    subtitle: "Mobile Money",
    bgClass: "bg-tertiary-500/20",
    delay: 2,
  },
  {
    x: 85,
    y: 70,
    icon: "lucide:bar-chart",
    title: "Analytics",
    subtitle: "Track everything",
    bgClass: "bg-secondary-500/20",
    delay: 3,
  },
]);

const handleMouseMove = (e) => {
  mousePosition.value = {
    x: e.clientX,
    y: e.clientY,
  };
};

const handleGetStarted = () => {
  emit("get-started");
  // For direct redirect:
  window.location.href = "/signup";
};

const handleWatchDemo = () => {
  emit("watch-demo");
  // For direct redirect:
  // window.location.href = "https://demo.bookiime.com"
};

onMounted(() => {
  mounted.value = true;
  window.addEventListener("mousemove", handleMouseMove);
});

onUnmounted(() => {
  window.removeEventListener("mousemove", handleMouseMove);
});
</script>

<style scoped>
@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(60px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes float {
  0%,
  100% {
    transform: translateY(-15px) rotate(0deg);
  }
  50% {
    transform: translateY(15px) rotate(180deg);
  }
}

@keyframes float-gentle {
  0%,
  100% {
    transform: translateY(-8px);
  }
  50% {
    transform: translateY(8px);
  }
}

@keyframes pulse-glow {
  0%,
  100% {
    box-shadow: 0 0 20px rgba(251, 191, 36, 0.3);
  }
  50% {
    box-shadow: 0 0 40px rgba(251, 191, 36, 0.6);
  }
}

.animate-fade-in-up {
  animation: fade-in-up 1s ease-out forwards;
}

.animate-float {
  animation: float 8s ease-in-out infinite;
}

.animate-float-gentle {
  animation: float-gentle 6s ease-in-out infinite;
}

.animate-pulse-glow {
  animation: pulse-glow 3s ease-in-out infinite;
}
</style>
