// https://nuxt.com/docs/api/configuration/nuxt-config
import tailwindcss from "@tailwindcss/vite";

export default defineNuxtConfig({
  modules: [
    "@nuxt/fonts",
    "@nuxt/icon",
    "shadcn-nuxt",
  ],
   // @ts-expect-error: Nuxt runtime will still accept this, TS defs lag behind.
  server: {
    port: process.env.PORT ? parseInt( process.env.PORT) : 3000,
  },
  compatibilityDate: '2025-05-15',
  devtools: { enabled: true },
  css: ['~/assets/css/main.css'],
   shadcn: {
    prefix: '',
    /**
     * @default "./components/ui"
     */
    componentDir: './components/ui'
  },
  vite: {
    plugins: [tailwindcss()],
  },
  runtimeConfig: {
    public: {
      apiBase: process.env.NUXT_PUBLIC_API_BASE,
    },
  },
  app: {
    head: {
      link: [
        { rel: 'icon', type: 'image/png', href: '/favicon.png' },
      ],
    },
  },
})
