{"name": "destr", "version": "1.2.2", "description": "A faster, secure and convenient alternative for JSON.parse", "repository": "unjs/destr", "license": "MIT", "type": "module", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["dist"], "devDependencies": {"@hapi/bourne": "^3.0.0", "@vitest/coverage-c8": "^0.25.3", "benchmark": "^2.1.4", "eslint": "^8.29.0", "eslint-config-unjs": "^0.0.2", "secure-json-parse": "^2.6.0", "standard-version": "^9.5.0", "typescript": "^4.9.3", "unbuild": "^1.0.1", "vitest": "^0.25.3"}, "packageManager": "pnpm@7.18.0", "scripts": {"bench": "pnpm build && node ./bench.cjs", "build": "unbuild", "dev": "vitest dev", "lint": "eslint --ext .ts .", "release": "pnpm test && pnpm build && standard-version && git push --follow-tags && pnpm publish", "test": "pnpm lint && vitest run --coverage"}}