{"name": "ohmy<PERSON>tch", "version": "0.4.21", "description": "oh-my-fetch", "repository": "unjs/ohmyfetch", "license": "MIT", "sideEffects": false, "type": "module", "exports": {".": {"node": {"types": "./dist/node.d.ts", "import": "./dist/node.mjs", "require": "./cjs/node.cjs"}, "default": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./cjs/index.cjs"}}, "./node": {"types": "./dist/node.d.ts", "import": "./dist/node.mjs", "require": "./cjs/node.cjs"}, "./undici": {"types": "./dist/undici.d.ts", "import": "./dist/undici.mjs", "require": "./cjs/undici.cjs"}}, "main": "./cjs/node.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["dist", "node.d.ts", "undici.d.ts", "cjs"], "dependencies": {"destr": "^1.2.0", "node-fetch-native": "^0.1.8", "ufo": "^0.8.6", "undici": "^5.12.0"}, "devDependencies": {"@nuxtjs/eslint-config-typescript": "^11.0.0", "@types/node": "^18.11.9", "@vitest/coverage-c8": "^0.24.5", "eslint": "^8.26.0", "fetch-blob": "^3.2.0", "formdata-polyfill": "^4.0.10", "h3": "^0.8.6", "jiti": "^1.16.0", "listhen": "^0.3.5", "standard-version": "^9.5.0", "typescript": "^4.8.4", "unbuild": "^0.9.4", "vitest": "^0.24.5"}, "packageManager": "pnpm@7.14.2", "scripts": {"build": "unbuild", "dev": "vitest", "lint": "eslint --ext .ts .", "play": "jiti playground/index.ts", "release": "pnpm test && standard-version && git push --follow-tags && pnpm publish", "test": "pnpm lint && vitest run --coverage"}}