export type MessageType = "error" | "info" | "success" | "warning";
export type MessageOptions = {
  type?: MessageType;
  timeout?: number;
  title?: string;
};

// This composable provides a toast notification system
const toastState = ref<{
  message: string;
  options: MessageOptions;
} | null>(null);
let timeoutId: ReturnType<typeof setTimeout> | null = null;

// This function is used to show a toast notification
export const useToast = () => {
  const dismiss = () => {
    if (timeoutId) {
      clearTimeout(timeoutId);
      timeoutId = null;
    }
    toastState.value = null;
  };

  return { toastState, dismiss };
};

// This function is used to trigger a toast notification
export const $toast = (message: string, options: MessageOptions = {}) => {
  if (timeoutId) {
    clearTimeout(timeoutId);
  }

  toastState.value = {
    message,
    options: {
      type: 'error',
      timeout: 5000,
      ...options
    }
  }; // This sets the toast state with the message and options
  // Example usage: $toast('This is an error message', { type: 'error', timeout: 3000 });

  timeoutId = setTimeout(() => {
    toastState.value = null;
    timeoutId = null;
  }, toastState.value.options.timeout); // This clears the toast after the specified timeout
};