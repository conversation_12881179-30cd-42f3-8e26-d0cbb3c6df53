<template>
  <div class="hidden lg:block relative z-10 text-center max-w-lg px-8">
    <div class="mb-8 animate-fade-in-up">
      <div
        class="inline-flex items-center justify-center w-24 h-24 bg-white/20 backdrop-blur-sm rounded-3xl mb-6 shadow-2xl"
      >
        <img
          src="/public/favicon.png"
          alt="Bookiime Logo"
          class="w-12 h-12 object-contain"
        />
      </div>
      <p class="text-body-1 text-neutral-600 leading-relaxed">
        The future of scheduling is here. Streamline your bookings with
        AI-powered automation.
      </p>
    </div>

    <div
      class="relative mb-12 animate-fade-in-up"
      style="animation-delay: 0.3s"
    >
      <div
        class="primary-background backdrop-blur-sm rounded-2xl p-6 shadow-2xl border border-white/20 transform rotate-2 hover:rotate-0 transition-transform duration-500"
      >
        <div class="flex items-center space-x-4 mb-4">
          <div
            class="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center"
          >
            <Icon name="lucide:calendar-check" class="w-6 h-6 text-white" />
          </div>
          <div class="text-left">
            <h3 class="text-body-2 font-semibold text-white">
              Smart Scheduling
            </h3>
            <p class="text-body-4 text-white/70">
              AI-powered appointment booking
            </p>
          </div>
        </div>
        <div class="space-y-2">
          <div class="flex items-center space-x-2">
            <div class="w-2 h-2 bg-green-400 rounded-full"></div>
            <span class="text-body-4 text-white/80"
              >Available slots detected</span
            >
          </div>
          <div class="flex items-center space-x-2">
            <div class="w-2 h-2 bg-tertiary-400 rounded-full"></div>
            <span class="text-body-4 text-white/80"
              >Conflicts resolved automatically</span
            >
          </div>
        </div>
      </div>

      <div
        class="absolute -top-4 -right-4 bg-white/10 backdrop-blur-sm rounded-xl p-4 shadow-xl border border-white/20 transform -rotate-12 hover:rotate-0 transition-transform duration-500"
      >
        <div class="flex items-center space-x-2">
          <Icon name="lucide:users" class="w-5 h-5 text-white" />
          <span class="text-body-4 text-white font-medium">Team Sync</span>
        </div>
      </div>

      <div
        class="absolute -bottom-4 -left-4 bg-white/10 backdrop-blur-sm rounded-xl p-4 shadow-xl border border-white/20 transform rotate-12 hover:rotate-0 transition-transform duration-0"
      >
        <div class="flex items-center space-x-2">
          <Icon name="lucide:bell" class="w-5 h-5 text-white" />
          <span class="text-body-4 text-white font-medium">Smart Alerts</span>
        </div>
      </div>
    </div>

    <div
      class="grid grid-cols-3 gap-6 animate-fade-in-up"
      style="animation-delay: 0.6s"
    >
      <div class="text-center">
        <div class="text-h5 font-bold gt-primary mb-2">99.9%</div>
        <div class="text-body-4 text-neutral-600">Uptime</div>
      </div>
      <div class="text-center">
        <div class="text-h5 font-bold gt-primary mb-2">0+</div>
        <div class="text-body-4 text-neutral-600">Bookings</div>
      </div>
      <div class="text-center">
        <div class="text-h5 font-bold gt-primary mb-2">24/7</div>
        <div class="text-body-4 text-neutral-600">Support</div>
      </div>
    </div>

    <div
      class="flex justify-center space-x-2 mt-8 animate-fade-in-up"
      style="animation-delay: 0.9s"
    >
      <div class="w-8 h-2 bg-neutral-600 rounded-full"></div>
      <div class="w-2 h-2 bg-neutral-600 rounded-full"></div>
      <div class="w-2 h-2 bg-neutral-600 rounded-full"></div>
    </div>
  </div>
</template>
