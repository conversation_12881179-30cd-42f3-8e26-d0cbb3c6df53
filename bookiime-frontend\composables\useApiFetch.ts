import type { UseFetchOptions } from "#app";

// This composable provides a reusable API fetch function for GET requests
// It uses the Nuxt useFetch composable to handle API requests
// It includes error handling and authentication management
// The function returns a typed response based on the provided type parameters

export const useApiFetch = <T, R = T>(
  url: string,
  options: Omit<UseFetchOptions<T, R>, "lazy"> = {}
) => {
  const { $auth, $logout } = useNuxtApp(); // Access the auth plugin for authentication management
  const config = useRuntimeConfig();
  const fullUrl = `${config.public.apiBase}${url}`;

  const fetchOptions = {
    ...options,
    lazy: false, // TODO: switch to true when it's time to handle lazy loading
    onRequest({ options }) {
      options.headers.set("Authorization", `Bearer ${$auth.value.token}`);
      options.headers.set("Accept", "application/json");
    },
    onResponseError({ response }) {
      if (response.status === 401) {
        $logout();
        // navigateTo("/login");
      }
    },
  } as UseFetchOptions<T, R>;

  return useFetch(fullUrl, fetchOptions); // This returns a fetch function that can be used to make API requests
};
