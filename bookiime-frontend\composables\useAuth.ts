// export function useAuth() {
//   const token = useCookie('token') // or useSessionStorage/localStorage if you prefer

//   const isLoggedIn = computed(() => !!token.value)

//   return {
//     token,
//     isLoggedIn,
//   }
// }
// composables/useAuth.ts

export function useAuth() {
  const token = useCookie('token', { httpOnly: false }) // Assuming JWT is in browser

  const user = useState<any>('user', () => null)
  const isLoggedIn = computed(() => !!user.value)
  const config = useRuntimeConfig()

  async function validate() {
    if (!token.value) return

    try {
      const res = await $fetch<{ user: any }>(`${config.public.apiBase}/auth/validate-token`, {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${token.value}`,
        },
      })

      user.value = res.user // You expect the backend to return user data
    } catch (err) {
      console.error('Token invalid or expired')
      token.value = null
      user.value = null
    }
  }

  return {
    token,
    user,
    isLoggedIn,
    validate
  }
}
