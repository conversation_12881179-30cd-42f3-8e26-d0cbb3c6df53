// Simple auth middleware - only protects routes that explicitly use it
export default defineNuxtRouteMiddleware((to, from) => {
  const { $isLoggedIn } = useNuxtApp()
  const publicRoutes: string[] = [
    '/',
    '/login',
    '/signup',
  ]

  const isPublicRoute = publicRoutes.some((route: string) => {return to.path === route || to.path.startsWith(`${route}/`) })

  const isAuthPage = [
    '/login',
    '/signup',
  ].includes(useRoute().path)

  if (isPublicRoute) {
    return
  }

  if (!$isLoggedIn.value && !isAuthPage) {
    return navigateTo('/login')
  }
})



