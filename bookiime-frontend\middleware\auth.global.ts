// Simple auth middleware - only protects routes that explicitly use it
export default defineNuxtRouteMiddleware(() => {
  const { $isLoggedIn } = useNuxtApp()
  const publicPages = [
    '/',
    '/login',
    '/signup',
    // '/email-verification',
    // '/forgot-password',
    // '/reset-password',
  ]

  const isAuthPage = [
    '/login',
    '/users',
  ].includes(useRoute().path)

  if (!$isLoggedIn.value && !isAuthPage) {
    return navigateTo('/login')
  }
  // Only check authentication for routes that explicitly use this middleware
  if ($isLoggedIn.value && isAuthPage) {
    return navigateTo('/dashboard')
  }
  return true
})





