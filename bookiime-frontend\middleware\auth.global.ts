// Simple auth middleware - only protects routes that explicitly use it
export default defineNuxtRouteMiddleware((to, from) => {
  const { $isLoggedIn } = useNuxtApp()
  const publicRoutes: string[] = [
    '/',
    '/login',
    '/signup',
  ]

  const isPublicRoute = publicRoutes.some((route: string) => {return to.path === route || to.path.startsWith(`${route}/`) })

  const isAuthPage = [
    '/login',
    '/signup',
  ].includes(useRoute().path)

  // Redirect authenticated users away from auth pages to dashboard
  if ($isLoggedIn.value && isAuthPage) {
    return navigateTo('/dashboard')
  }

  // Allow access to public routes
  if (isPublicRoute) {
    return
  }

  // Redirect unauthenticated users to login
  if (!$isLoggedIn.value) {
    return navigateTo('/login')
  }
})