{"name": "ufo", "version": "0.8.6", "description": "URL utils for humans", "repository": "unjs/ufo", "license": "MIT", "sideEffects": false, "exports": {".": {"require": "./dist/index.cjs", "import": "./dist/index.mjs"}, "./*": "./*"}, "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["dist"], "devDependencies": {"@nuxtjs/eslint-config-typescript": "^11.0.0", "@types/node": "^18.11.0", "c8": "^7.12.0", "eslint": "^8.25.0", "standard-version": "^9.5.0", "typescript": "^4.8.4", "unbuild": "^0.9.2", "vitest": "^0.24.3"}, "packageManager": "pnpm@7.13.4", "scripts": {"build": "unbuild", "dev": "vitest", "lint": "eslint --ext .ts .", "release": "pnpm test && standard-version && git push --follow-tags && pnpm publish", "test": "pnpm lint && vitest run"}}