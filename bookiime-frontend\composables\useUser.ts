import type { User } from "@/types/user.types";

export const useUser = () => {
  const user = useCookie<User | null>("E0Zj-aH2v-7yBg-AEE9-YOdC", { // user cookie name
      maxAge: 60 * 60 * 24 * 2,
    path: "/",
    secure: process.env.NODE_ENV === "production",
    sameSite: "strict",
  });

  // This is a reactive state for user data
  const setUser = (data: User) => (user.value = data);

  // This is a method to clear user data
  const clearUser = () => (user.value = null);

  return { user, setUser, clearUser };
};