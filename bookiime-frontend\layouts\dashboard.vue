<template>
  <div class="min-h-screen bg-neutral-50">
    <!-- Navigation Header -->
    <header class="bg-white border-b border-neutral-200 sticky top-0 z-40">
      <div class="px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
          <!-- Logo and Brand -->
          <div class="flex items-center">
            <div class="flex-shrink-0 flex items-center">
              <Icon name="lucide:calendar-check" class="w-8 h-8 text-primary-600" />
              <span class="ml-2 text-xl font-bold text-neutral-900">Bookiime</span>
            </div>
          </div>

          <!-- Navigation Links -->
          <nav class="hidden md:flex space-x-8">
            <NuxtLink
              to="/dashboard"
              class="text-neutral-600 hover:text-primary-600 px-3 py-2 rounded-md text-sm font-medium transition-colors"
              active-class="text-primary-600 bg-primary-50"
            >
              Dashboard
            </NuxtLink>
            <NuxtLink
              to="/dashboard/bookings"
              class="text-neutral-600 hover:text-primary-600 px-3 py-2 rounded-md text-sm font-medium transition-colors"
              active-class="text-primary-600 bg-primary-50"
            >
              Bookings
            </NuxtLink>
            <NuxtLink
              to="/dashboard/customers"
              class="text-neutral-600 hover:text-primary-600 px-3 py-2 rounded-md text-sm font-medium transition-colors"
              active-class="text-primary-600 bg-primary-50"
            >
              Customers
            </NuxtLink>
            <NuxtLink
              to="/dashboard/services"
              class="text-neutral-600 hover:text-primary-600 px-3 py-2 rounded-md text-sm font-medium transition-colors"
              active-class="text-primary-600 bg-primary-50"
            >
              Services
            </NuxtLink>
            <NuxtLink
              to="/dashboard/settings"
              class="text-neutral-600 hover:text-primary-600 px-3 py-2 rounded-md text-sm font-medium transition-colors"
              active-class="text-primary-600 bg-primary-50"
            >
              Settings
            </NuxtLink>
          </nav>

          <!-- User Menu -->
          <div class="flex items-center space-x-4">
            <!-- Notifications -->
            <button class="text-neutral-400 hover:text-neutral-600 transition-colors">
              <Icon name="lucide:bell" class="w-6 h-6" />
            </button>

            <!-- User Dropdown -->
            <div class="relative">
              <button
                @click="showUserMenu = !showUserMenu"
                class="flex items-center space-x-2 text-neutral-600 hover:text-neutral-900 transition-colors"
              >
                <div class="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                  <Icon name="lucide:user" class="w-4 h-4 text-primary-600" />
                </div>
                <span class="hidden md:block text-sm font-medium">{{ user?.email || 'User' }}</span>
                <Icon name="lucide:chevron-down" class="w-4 h-4" />
              </button>

              <!-- Dropdown Menu -->
              <div
                v-if="showUserMenu"
                class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg border border-neutral-200 py-1 z-50"
              >
                <NuxtLink
                  to="/dashboard/profile"
                  class="block px-4 py-2 text-sm text-neutral-700 hover:bg-neutral-50 transition-colors"
                >
                  Profile
                </NuxtLink>
                <NuxtLink
                  to="/dashboard/settings"
                  class="block px-4 py-2 text-sm text-neutral-700 hover:bg-neutral-50 transition-colors"
                >
                  Settings
                </NuxtLink>
                <hr class="my-1 border-neutral-200" />
                <button
                  @click="handleLogout"
                  class="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors"
                >
                  Sign Out
                </button>
              </div>
            </div>

            <!-- Mobile Menu Button -->
            <button
              @click="showMobileMenu = !showMobileMenu"
              class="md:hidden text-neutral-600 hover:text-neutral-900 transition-colors"
            >
              <Icon name="lucide:menu" class="w-6 h-6" />
            </button>
          </div>
        </div>
      </div>

      <!-- Mobile Navigation -->
      <div v-if="showMobileMenu" class="md:hidden border-t border-neutral-200">
        <div class="px-2 pt-2 pb-3 space-y-1">
          <NuxtLink
            to="/dashboard"
            class="block px-3 py-2 rounded-md text-base font-medium text-neutral-600 hover:text-primary-600 hover:bg-primary-50 transition-colors"
            active-class="text-primary-600 bg-primary-50"
          >
            Dashboard
          </NuxtLink>
          <NuxtLink
            to="/dashboard/bookings"
            class="block px-3 py-2 rounded-md text-base font-medium text-neutral-600 hover:text-primary-600 hover:bg-primary-50 transition-colors"
            active-class="text-primary-600 bg-primary-50"
          >
            Bookings
          </NuxtLink>
          <NuxtLink
            to="/dashboard/customers"
            class="block px-3 py-2 rounded-md text-base font-medium text-neutral-600 hover:text-primary-600 hover:bg-primary-50 transition-colors"
            active-class="text-primary-600 bg-primary-50"
          >
            Customers
          </NuxtLink>
          <NuxtLink
            to="/dashboard/services"
            class="block px-3 py-2 rounded-md text-base font-medium text-neutral-600 hover:text-primary-600 hover:bg-primary-50 transition-colors"
            active-class="text-primary-600 bg-primary-50"
          >
            Services
          </NuxtLink>
          <NuxtLink
            to="/dashboard/settings"
            class="block px-3 py-2 rounded-md text-base font-medium text-neutral-600 hover:text-primary-600 hover:bg-primary-50 transition-colors"
            active-class="text-primary-600 bg-primary-50"
          >
            Settings
          </NuxtLink>
        </div>
      </div>
    </header>

    <!-- Main Content -->
    <main class="flex-1">
      <slot />
    </main>
  </div>
</template>

<script setup lang="ts">
const { user } = useUser()
const { $logout } = useNuxtApp()

const showUserMenu = ref(false)
const showMobileMenu = ref(false)

const handleLogout = async () => {
  await $logout()
}

// Close menus when clicking outside
onMounted(() => {
  document.addEventListener('click', (event) => {
    const target = event.target as HTMLElement
    if (!target.closest('.relative')) {
      showUserMenu.value = false
    }
    if (!target.closest('.md\\:hidden')) {
      showMobileMenu.value = false
    }
  })
})
</script>
