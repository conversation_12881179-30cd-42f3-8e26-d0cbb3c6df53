import { f as fetch, _ as _Blob, F as File, a as FormData, H as Headers, R as Request, b as Response, A as AbortController } from './shared/node-fetch-native.b703cef9.mjs';
import 'node:fs';
import 'node:path';
import 'node:http';
import 'node:https';
import 'node:zlib';
import 'node:stream';
import 'node:buffer';
import 'node:util';
import 'node:url';
import 'node:net';

globalThis.fetch = globalThis.fetch || fetch;
globalThis.Blob = globalThis.Blob || _Blob;
globalThis.File = globalThis.File || File;
globalThis.FormData = globalThis.FormData || FormData;
globalThis.Headers = globalThis.Headers || Headers;
globalThis.Request = globalThis.Request || Request;
globalThis.Response = globalThis.Response || Response;
globalThis.AbortController = globalThis.AbortController || AbortController;
