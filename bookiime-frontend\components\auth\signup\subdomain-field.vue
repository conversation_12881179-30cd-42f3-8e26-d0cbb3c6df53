<template>
  <div class="space-y-2">
    <label class="block">
      <p class="text-foreground text-base font-medium leading-normal pb-2">Subdomain</p>
      <div class="relative">
        <input
          :value="modelValue"
          @input="handleInput"
          type="text"
          placeholder="Enter your subdomain"
          class="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-foreground focus:outline-0 focus:ring-2 focus:ring-primary border border-border bg-background focus:border-primary h-14 placeholder:text-muted-foreground p-[15px] text-base font-normal leading-normal pr-32"
          :class="{ 'border-red-500': error }"
          required
        />
        <span class="absolute right-4 top-1/2 transform -translate-y-1/2 text-muted-foreground text-sm">
          <span v-if="checking" class="mr-2">
            <Icon name="lucide:loader-2" class="w-4 h-4 animate-spin inline" />
          </span>
          .bookiime.com
        </span>
      </div>
      <p v-if="error" class="text-red-500 text-sm mt-1">{{ error }}</p>
    </label>
  </div>
</template>

<script setup lang="ts">
interface Props {
  modelValue: string
}

interface Emits {
  (e: 'update:modelValue', value: string): void
  (e: 'validation-change', isValid: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const error = ref('')
const checking = ref(false)
const debounceTimer = ref<NodeJS.Timeout>()

const handleInput = (event: Event) => {
  const value = (event.target as HTMLInputElement).value.toLowerCase()
  emit('update:modelValue', value)
  
  clearTimeout(debounceTimer.value)
  debounceTimer.value = setTimeout(() => {
    validateSubdomain(value)
  }, 500)
}

const validateSubdomain = async (subdomain: string) => {
  if (!subdomain) {
    error.value = ''
    emit('validation-change', false)
    return
  }

  if (subdomain.length < 3) {
    error.value = 'Subdomain must be at least 3 characters'
    emit('validation-change', false)
    return
  }

  if (!/^[a-z0-9-]+$/.test(subdomain)) {
    error.value = 'Only lowercase letters, numbers, and hyphens allowed'
    emit('validation-change', false)
    return
  }

  if (subdomain.startsWith('-') || subdomain.endsWith('-')) {
    error.value = 'Subdomain cannot start or end with a hyphen'
    emit('validation-change', false)
    return
  }

  // Check reserved words
  const reservedWords = ['admin', 'api', 'www', 'test', 'demo', 'app', 'mail', 'ftp', 'blog', 'shop', 'store']
  if (reservedWords.includes(subdomain)) {
    error.value = 'This subdomain is reserved'
    emit('validation-change', false)
    return
  }

  // Check availability with backend API
  checking.value = true
  try {
    const config = useRuntimeConfig()
    const response = await $fetch<{ available: boolean, suggestions?: string[] }>(
      `${config.public.apiBase}/users/check-subdomain/${subdomain}`,
      {
        method: 'GET',
        headers: {
          'Accept': 'application/json'
        }
      }
    )

    if (response.available) {
      error.value = ''
      emit('validation-change', true)
    } else {
      error.value = 'This subdomain is already taken'
      emit('validation-change', false)
    }
  } catch (err: any) {
    // If backend endpoint doesn't exist (404) or other errors, handle gracefully
    console.warn('Subdomain check failed:', err)

    if (err.status === 404) {
      // Endpoint doesn't exist yet - allow to continue
      error.value = ''
      emit('validation-change', true)
      console.info('Subdomain check endpoint not implemented yet - allowing to continue')
    } else {
      // Other errors - show warning but allow to continue
      error.value = 'Unable to verify availability. Registration will continue.'
      emit('validation-change', true)
    }
  } finally {
    checking.value = false
  }
}
</script>