<template>
  <div class="min-h-screen bg-gradient-to-br from-background via-neutral-150 to-neutral-200 relative overflow-hidden">
    <!-- Animated background -->
    <div class="absolute inset-0 overflow-hidden pointer-events-none">
      <!-- Gradient Orbs -->
      <div class="absolute -top-40 -right-40 w-80 h-80 primary-background rounded-full blur-3xl opacity-20 animate-pulse"></div>
      <div class="absolute -bottom-40 -left-40 w-80 h-80 secondary-background rounded-full blur-3xl opacity-20 animate-pulse" style="animation-delay: 1s"></div>
      <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 tertiary-background rounded-full blur-3xl opacity-10 animate-pulse" style="animation-delay: 2s"></div>

      <!-- Floating Particles -->
      <div class="absolute top-1/4 left-1/6 w-4 h-4 bg-tertiary-400 rounded-full animate-bounce opacity-70" style="animation-delay: 0.2s"></div>
      <div class="absolute top-1/3 right-1/3 w-3 h-3 bg-green-400 rounded-full animate-bounce opacity-70" style="animation-delay: 0.8s"></div>
      <div class="absolute bottom-1/3 left-1/3 w-5 h-5 bg-secondary-300 rounded-full animate-bounce opacity-70" style="animation-delay: 1.2s"></div>
      <div class="absolute bottom-1/3 right-1/4 w-2 h-2 bg-white rounded-full animate-bounce opacity-80" style="animation-delay: 0.6s"></div>
    </div>

    <div class="relative z-10 min-h-screen flex flex-col lg:flex-row">
      <!-- Left side - Showcase -->
      <div class="lg:flex-1 lg:primary-background lg:relative lg:overflow-hidden lg:flex lg:items-center lg:justify-center p-4 lg:p-0">
        <!-- Dark overlay -->
        <div class="absolute inset-0 bg-black/20 lg:block hidden"></div>

        <!-- Showcase -->
        <div class="hidden lg:flex relative z-20 w-full max-w-4xl mx-auto px-8 items-center justify-between">
          <!-- Left content -->
          <div class="flex-1 max-w-lg">
            <div class="mb-8 animate-fade-in-up">
              <div class="inline-flex items-center justify-center w-20 h-20 bg-white rounded-2xl mb-6 shadow-2xl">
                <img src="/favicon.png" alt="Bookiime Logo" class="w-10 h-10 object-contain" />
              </div>
              <h1 class="text-4xl font-bold text-white mb-4 drop-shadow-lg">Join Bookiime</h1>
              <p class="text-lg text-white leading-relaxed drop-shadow-md mb-8">
                Start your journey with intelligent scheduling. Create your account and transform how you manage appointments.
              </p>
            </div>

            <!-- Features -->
            <div class="space-y-4 animate-fade-in-up" style="animation-delay: 0.3s">
              <div class="flex items-center space-x-4 primary-background backdrop-blur-sm rounded-xl p-4 border border-white/20 shadow-xl transform hover:scale-105 transition-transform duration-300">
                <div class="w-10 h-10 bg-white/20 rounded-lg flex items-center justify-center">
                  <Icon name="lucide:zap" class="w-5 h-5 text-white" />
                </div>
                <div>
                  <h3 class="text-white font-semibold drop-shadow-md">Quick Setup</h3>
                  <p class="text-white/90 text-sm drop-shadow-sm">Get started in minutes</p>
                </div>
              </div>

              <div class="flex items-center space-x-4 primary-background backdrop-blur-sm rounded-xl p-4 border border-white/20 shadow-xl transform hover:scale-105 transition-transform duration-300">
                <div class="w-10 h-10 bg-white/20 rounded-lg flex items-center justify-center">
                  <Icon name="lucide:globe" class="w-5 h-5 text-white" />
                </div>
                <div>
                  <h3 class="text-white font-semibold drop-shadow-md">Custom Subdomain</h3>
                  <p class="text-white/90 text-sm drop-shadow-sm">Your unique booking page</p>
                </div>
              </div>

              <div class="flex items-center space-x-4 primary-background backdrop-blur-sm rounded-xl p-4 border border-white/20 shadow-xl transform hover:scale-105 transition-transform duration-300">
                <div class="w-10 h-10 bg-white/20 rounded-lg flex items-center justify-center">
                  <Icon name="lucide:clock" class="w-5 h-5 text-white" />
                </div>
                <div>
                  <h3 class="text-white font-semibold drop-shadow-md">24/7 Support</h3>
                  <p class="text-white/90 text-sm drop-shadow-sm">Always here to help</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Right side - Illustration -->
          <div class="flex-1 flex justify-center items-center animate-fade-in-up" style="animation-delay: 0.6s">
            <div class="relative">
              <img
                src="/Sign up-bro.png"
                alt="Sign up illustration"
                class="w-80 h-80 object-contain drop-shadow-2xl"
              />
              <!-- Subtle glow effect -->
              <div class="absolute inset-0 bg-white/5 rounded-full blur-3xl"></div>
            </div>
          </div>
        </div>

        <!-- Mobile: Header -->
        <div class="lg:hidden text-center animate-fade-in-up relative z-20">
          <div class="inline-flex items-center justify-center w-24 h-24 bg-white rounded-3xl mb-4 shadow-2xl">
            <img src="/favicon.png" alt="Bookiime Logo" class="w-12 h-12 object-contain" />
          </div>
          <h1 class="text-2xl font-bold text-neutral-900 mb-2">Join Bookiime</h1>
          <p class="text-body-2 text-neutral-600 animate-fade-in" style="animation-delay: 0.3s">
            Join the scheduling revolution
          </p>
        </div>
      </div>

      <!-- Right side - Form -->
      <div class="lg:flex-1 lg:bg-white lg:relative lg:flex lg:items-center lg:justify-center flex-1 flex items-center justify-center p-4">
        <div class="w-full max-w-md">
          <!-- Desktop Header -->
          <div class="hidden lg:block text-center mb-8">
            <div class="inline-flex items-center justify-center w-16 h-16 bg-neutral-100 rounded-2xl mb-6">
              <Icon name="lucide:user-plus" class="w-8 h-8 text-primary-600" />
            </div>
            <h2 class="text-h4 font-bold text-neutral-900 mb-2">Create Account</h2>
            <p class="text-body-2 text-neutral-600">
              Start your journey with intelligent scheduling
            </p>
          </div>

          <div class="lg:bg-transparent lg:backdrop-blur-none lg:shadow-none lg:border-none lg:p-0 bg-white/80 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/20 p-8 animate-fade-in-up" style="animation-delay: 0.5s">
            <auth-signup-form />

            <div class="text-center pt-4">
              <p class="text-body-3 text-neutral-600">
                Already have an account?
                <NuxtLink
                  to="/login"
                  class="text-primary-600 hover:text-primary-700 font-medium transition-colors ml-1"
                >
                  Sign In
                </NuxtLink>
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// No middleware needed - open to all users

useHead({
  title: 'Create Account - Bookiime',
  meta: [
    { name: 'description', content: 'Create your Bookiime account and start your scheduling journey' }
  ]
})
</script>

<style scoped>
@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.animate-fade-in-up {
  animation: fade-in-up 0.8s ease-out forwards;
}

.animate-fade-in {
  animation: fade-in 0.8s ease-out forwards;
}
</style>
